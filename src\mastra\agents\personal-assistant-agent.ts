import { Agent } from "@mastra/core/agent";
import { createOpenAICompatible } from "@ai-sdk/openai-compatible";
import { Memory } from "@mastra/memory";
import { LibSQLStore } from "@mastra/libsql";
import { mcpTools } from "../mcp";
const llmModelName = process.env.LLM_MODEL_NAME;
if (!llmModelName) {
  throw new Error("Missing environment variable: LLM_MODEL_NAME");
}

const openaiBaseUrl = process.env.OPENAI_BASE_URL;
if (!openaiBaseUrl) {
  throw new Error("Missing environment variable: OPENAI_BASE_URL");
}

const openaiApiKey = process.env.OPENAI_API_KEY;
if (!openaiApiKey) {
  throw new Error("Missing environment variable: OPENAI_API_KEY");
}
const openaiCompatible = createOpenAICompatible({
  name: llmModelName,
  baseURL: openaiBaseUrl,
  apiKey: openaiApiKey,
  headers: {},
  queryParams: {},
  fetch: async (url, options) => {
    // custom fetch logic
    return fetch(url, options);
  },
});
export const personalAssistantAgent = new Agent({
  name: "Personal Assistant",
  instructions: `
    You are a helpful personal assistant that can help with various tasks such as searching web 
    and scheduling social media posts.
    
    You have access to the following tools:
    
    1. firecrawl:
       - Use these tools for searching web content
    2. Hackernews:
       - Use this tool to search for stories on Hackernews
       - You can use it to get the top stories or specific stories
       - You can use it to retrieve comments for stories    
    Keep your responses concise and friendly.
  `,
  model: openaiCompatible(llmModelName),
  memory: new Memory({
    storage: new LibSQLStore({
      url: "file:../../memory.db", // local file-system database. Location is relative to the output directory `.mastra/output`
    }),
  }), // Add memory here
  tools: { ...mcpTools }, // Add the MCP tools to the personal assistant agent's tools
});
