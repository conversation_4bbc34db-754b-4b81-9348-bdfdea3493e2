import { <PERSON><PERSON> } from "@mastra/core/mastra";
import { <PERSON><PERSON><PERSON>og<PERSON> } from "@mastra/loggers";
import { LibSQLStore } from "@mastra/libsql";
import { personalAssistantAgent } from "./agents/personal-assistant-agent";
import { askWebAgent } from "./agents/askweb-agent";
import { mcpTools } from "./mcp";

// Add the MCP tools to the personal assistant agent's tools
// Object.assign(personalAssistantAgent.tools, mcpTools);

export const mastra = new Mastra({
  agents: {
    personalAssistantAgent,
    askWebAgent,
  },
  storage: new LibSQLStore({
    url: ":memory:",
  }),
  logger: new PinoLogger({
    name: "<PERSON><PERSON>",
    level: "info",
  }),
});
