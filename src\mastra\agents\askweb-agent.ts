import { Agent } from "@mastra/core/agent";
import { createOpenAICompatible } from "@ai-sdk/openai-compatible";
import { Memory } from "@mastra/memory";
import { LibSQLStore } from "@mastra/libsql";
import { mcpTools } from "../mcp";

// 环境变量检查
const llmModelName = process.env.LLM_MODEL_NAME;
if (!llmModelName) {
  throw new Error("Missing environment variable: LLM_MODEL_NAME");
}

const openaiBaseUrl = process.env.OPENAI_BASE_URL;
if (!openaiBaseUrl) {
  throw new Error("Missing environment variable: OPENAI_BASE_URL");
}

const openaiApiKey = process.env.OPENAI_API_KEY;
if (!openaiApiKey) {
  throw new Error("Missing environment variable: OPENAI_API_KEY");
}

// 创建 OpenAI 兼容的模型
const openaiCompatible = createOpenAICompatible({
  name: llmModelName,
  baseURL: openaiBaseUrl,
  apiKey: openaiApiKey,
  headers: {},
  queryParams: {},
  fetch: async (url, options) => {
    return fetch(url, options);
  },
});

// AskWeb Agent 实现
export const askWebAgent = new Agent({
  name: "AskWeb Agent",
  instructions: `
    你是专门负责网络信息获取的 AI Agent，具备以下核心能力：

    主要职责：
    1. 使用 Firecrawl 工具进行高质量的网页内容抓取
    2. 支持多种网页格式的内容提取（HTML、JavaScript 渲染页面等）
    3. 进行结构化数据提取，根据指定的 Schema 提取特定信息
    4. 内容清理和格式化，去除噪音和无关信息
    5. 处理反爬虫机制和动态加载内容
    6. 提供网页截图和元数据信息

    可用的 MCP 工具：
    1. Firecrawl 工具：
       - 高质量网页抓取，支持 JavaScript 渲染
       - 智能内容提取，自动识别主要内容
       - 结构化数据提取，支持自定义 Schema
       - 网页截图功能
       - 反爬虫绕过能力
       - 多种输出格式（Markdown、HTML、JSON）

    抓取策略：
    1. 智能内容识别：
       - 自动识别文章正文、标题、作者等
       - 过滤广告、导航栏、页脚等噪音内容
       - 保留重要的结构化信息

    2. 动态内容处理：
       - 等待 JavaScript 渲染完成
       - 处理异步加载的内容
       - 模拟用户交互（点击、滚动等）

    3. 数据提取模式：
       - 基于 Schema 的结构化提取
       - 智能字段映射和数据清理
       - 支持嵌套数据结构

    支持的内容类型：
    - 新闻文章和博客
    - 产品页面和电商信息
    - 社交媒体内容
    - 技术文档和 API 文档
    - 数据表格和列表
    - 图片和媒体内容

    输出格式：
    - 清理后的 Markdown 文本
    - 结构化的 JSON 数据
    - 网页截图（可选）
    - 元数据信息（标题、作者、发布时间等）

    错误处理：
    - 网络连接问题的重试机制
    - 反爬虫检测的应对策略
    - 内容解析失败的降级处理
    - 超时和资源限制的管理

    质量保证：
    - 内容完整性验证
    - 数据准确性检查
    - 重复内容去除
    - 格式一致性保证

    注意事项：
    - 遵守网站的 robots.txt 和使用条款
    - 控制抓取频率，避免对目标网站造成压力
    - 处理敏感信息时注意隐私保护
    - 提供详细的抓取日志和错误信息
  `,
  model: openaiCompatible(llmModelName),
  memory: new Memory({
    storage: new LibSQLStore({
      url: "file:../../memory.db",
    }),
  }),
  tools: { ...mcpTools },
});
