# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is an AI Report Agent system built on the Mastra framework that provides specialized AI agents for different domains including personal assistance, web scraping, financial analysis, and weather services. The system integrates multiple AI models, tools, and workflows to deliver comprehensive AI-powered services.

## Development Commands

### Core Development
```bash
# Install dependencies
pnpm install

# Development server
pnpm dev

# Build for production
pnpm build

# Start production server
pnpm start
```

### Mastra CLI Commands
```bash
# Development mode with hot reload
mastra dev

# Build the application
mastra build

# Start production server
mastra start
```

## Architecture Overview

### Core Framework
- **Mastra Framework**: TypeScript-based AI agent framework
- **Memory Management**: LibSQL-based persistent memory storage
- **Tool Integration**: MCP (Model Context Protocol) for external tool integration
- **Workflow Engine**: Step-based workflow orchestration

### Agent Architecture
The system consists of multiple specialized AI agents:

1. **Personal Assistant Agent** (`personal-assistant-agent.ts`): General purpose assistant with MCP tools
2. **AskWeb Agent** (`askweb-agent.ts`): Specialized web scraping and content extraction
3. **Financial Agent** (`financial-agent.ts`): Transaction analysis and financial insights
4. **Weather Agent** (`weather-agent.ts`): Weather information and activity planning

### Key Components

#### MCP Integration (`src/mastra/mcp.ts`)
- **Hacker News MCP**: Built-in Hacker News stories and comments access
- **Firecrawl MCP**: High-quality web scraping with JavaScript rendering (requires FIRECRAWL_API_KEY)
- **Error Handling**: Graceful fallback when MCP tools fail to load

#### Custom Tools (`src/mastra/tools/`)
- **Weather Tool**: Current weather data from Open-Meteo API
- **Transactions Tool**: Financial data from Google Sheets
- **Schema Validation**: Zod-based input/output validation

#### Workflows (`src/mastra/workflows/`)
- **Weather Workflow**: Multi-step weather forecasting and activity planning
- **Step Composition**: Chained execution of fetch and planning steps

## Configuration

### Environment Variables
```env
# AI Model Configuration
OPENAI_API_KEY=your_api_key_here
OPENAI_BASE_URL=https://ark.cn-beijing.volces.com/api/v3
LLM_MODEL_NAME=doubao-1.5-pro-32k-250115

# External Services
FIRECRAWL_API_KEY=your_firecrawl_key_here
COMPOSIO_MCP_GITHUB=your_composio_mcp_url

# Database (if needed)
MYSQL_HOST=your_mysql_host
MYSQL_PORT=3306
MYSQL_USER=your_mysql_user
MYSQL_PASS=your_mysql_password

# Application Settings
PORT=3000
NODE_ENV=development
DEBUG=true
```

### Memory Storage
- **Personal Assistant**: Uses `file:../../memory.db` for persistent memory
- **Weather Agent**: Uses `file:../mastra.db` for persistent memory
- **AskWeb Agent**: Uses `file:../../memory.db` for persistent memory
- **Financial Agent**: Uses `file:../../memory.db` for persistent memory

## Key Features

### Multi-Model Support
- **OpenAI Compatible**: Supports various OpenAI-compatible API providers
- **Custom Fetch**: Configurable fetch logic for API calls
- **Model Selection**: Environment variable-based model configuration

### Tool Ecosystem
- **MCP Tools**: Dynamic tool loading from MCP servers
- **Custom Tools**: Domain-specific tools with schema validation
- **Error Resilience**: Graceful handling of tool failures

### Workflow Capabilities
- **Step Chaining**: Sequential execution of workflow steps
- **Data Flow**: Automatic data passing between steps
- **Agent Integration**: Workflow steps can invoke agents

## Memory and Persistence

### Storage Architecture
- **LibSQL**: Embedded SQL database for memory storage
- **File-based**: Persistent storage across agent restarts
- **Relative Paths**: Memory files stored relative to `.mastra/output` directory

### Memory Features
- **Conversation History**: Maintains context across interactions
- **Tool Results**: Persists tool execution results
- **Agent State**: Maintains agent-specific memory state

## Development Guidelines

### Adding New Agents
1. Create agent file in `src/mastra/agents/`
2. Define instructions with clear role and capabilities
3. Configure appropriate tools and memory
4. Register agent in `src/mastra/index.ts`

### Creating Tools
1. Use `createTool` from `@mastra/core/tools`
2. Define Zod schemas for input/output validation
3. Implement `execute` function with error handling
4. Export tool for agent integration

### Building Workflows
1. Use `createStep` for individual workflow steps
2. Define input/output schemas with Zod
3. Chain steps with `.then()` composition
4. Commit workflow with `workflow.commit()`

### MCP Integration
1. Add server configuration in `src/mastra/mcp.ts`
2. Handle environment variable dependencies
3. Implement error handling for tool loading
4. Test MCP tool availability before use

## Error Handling Patterns

### MCP Tool Loading
- Graceful fallback when tools fail to load
- Console logging for debugging
- Empty object fallback to prevent crashes

### API Integration
- Custom fetch logic with error handling
- Environment variable validation
- Schema validation for inputs/outputs

### Memory Management
- File path validation
- Database connection handling
- Persistence across restarts

## Testing and Debugging

### Development Debugging
- Enable `DEBUG=true` for verbose logging
- Use `VERBOSE_LOGGING=true` for detailed output
- Check console for MCP tool loading status

### Common Issues
- **MCP Tools Not Loading**: Check environment variables and network connectivity
- **Memory Issues**: Verify file paths and database permissions
- **Model Configuration**: Validate API keys and base URLs

## File Structure

```
src/
├── mastra/
│   ├── agents/           # AI agent implementations
│   │   ├── personal-assistant-agent.ts
│   │   ├── askweb-agent.ts
│   │   ├── financial-agent.ts
│   │   └── weather-agent.ts
│   ├── tools/            # Custom tool implementations
│   │   ├── weather-tool.ts
│   │   └── get-transactions-tool.ts
│   ├── workflows/        # Workflow definitions
│   │   └── weather-workflow.ts
│   ├── index.ts          # Main Mastra configuration
│   └── mcp.ts            # MCP server integration
└── types/                # TypeScript type definitions
```

## Important Notes

- **Chinese Language**: Primary language for agent instructions and responses
- **Environment Variables**: Required for most external integrations
- **Memory Persistence**: Agents maintain conversation history across sessions
- **MCP Integration**: External tools loaded dynamically with graceful fallback
- **Multi-Agent Architecture**: Specialized agents for different domains
- **Workflow Composition**: Complex tasks broken into chained steps