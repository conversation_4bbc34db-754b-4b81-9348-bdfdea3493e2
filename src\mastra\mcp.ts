import { MCPClient } from "@mastra/mcp";
import path from "path";

// 构建服务器配置，只包含可用的服务
const servers: any = {};
// 添加 Hacker News MCP 服务器
servers.hackernews = {
  command: "npx",
  args: ["-y", "@devabdultech/hn-mcp-server"],
};
// 添加 Filesystem MCP 服务器
servers.textEditor = {
  command: "pnpx",
  args: [
    `@modelcontextprotocol/server-filesystem`,
    path.join(process.cwd(), "notes"), // 指向我们创建的 notes 目录
  ],
};
// Firecrawl MCP 服务器配置
if (process.env.FIRECRAWL_API_KEY) {
  servers.firecrawl = {
    command: "npx",
    args: ["-y", "firecrawl-mcp"],
    env: {
      FIRECRAWL_API_KEY: process.env.FIRECRAWL_API_KEY,
    },
  };
  console.log("🔧 Firecrawl MCP 服务器已配置");
} else {
  console.log(
    "⚠️  Firecrawl MCP 服务器未配置 - 缺少 FIRECRAWL_API_KEY 环境变量"
  );
}
const mcp = new MCPClient({
  servers,
});

// 使用 try-catch 处理工具获取错误
let mcpTools: any = {};
try {
  mcpTools = await mcp.getTools();
  console.log("✅ MCP 工具加载成功");
} catch (error) {
  console.error("❌ MCP 工具加载失败:", error);
  // 如果 MCP 工具加载失败，返回空对象以避免应用崩溃
  mcpTools = {};
}

export { mcpTools };
